import express, { Request, Response } from 'express';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Email routes disabled - using IMAP monitor instead
router.get('/messages', authenticateToken, async (req: Request, res: Response) => {
  res.json({ message: 'Email monitoring via IMAP - check server logs' });
});

router.get('/messages/:id', authenticateToken, async (req: Request, res: Response) => {
  res.json({ message: 'Email monitoring via IMAP - check server logs' });
});

export default router;