import express from 'express';
import ImapMonitorService from '../services/imapMonitor';

const router = express.Router();
let monitorService: ImapMonitorService | null = null;

// Get monitor status
router.get('/status', (req, res) => {
  res.json({
    running: monitorService !== null,
    message: monitorService ? 'Email monitor is running' : 'Email monitor is stopped'
  });
});

// Start monitor
router.post('/start', (req, res) => {
  if (monitorService) {
    return res.json({ message: 'Email monitor already running' });
  }
  
  monitorService = new ImapMonitorService();
  monitorService.start();
  
  res.json({ message: 'Email monitor started' });
});

// Stop monitor
router.post('/stop', (req, res) => {
  if (!monitorService) {
    return res.json({ message: 'Email monitor not running' });
  }
  
  monitorService.stop();
  monitorService = null;
  
  res.json({ message: 'Email monitor stopped' });
});

export default router;