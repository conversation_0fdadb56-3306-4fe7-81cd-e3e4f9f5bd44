import { useState, useEffect } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import LandingPage from './components/LandingPage';
import Login from './components/Login';
import Dashboard from './components/Dashboard';
import { User } from './types';
import { theme } from './theme';
import { config } from './config';

function App(): JSX.Element {
  const [token, setToken] = useState<string | null>(localStorage.getItem('token'));
  const [user, setUser] = useState<User | null>(null);
  const [showLogin, setShowLogin] = useState<boolean>(false);

  useEffect(() => {
    document.title = config.appName;
  }, []);

  useEffect(() => {
    if (token) {
      const userData = localStorage.getItem('user');
      if (userData) {
        try {
          setUser(JSON.parse(userData) as User);
        } catch (error) {
          console.error('Error parsing user data:', error);
          // Clear invalid data
          localStorage.removeItem('user');
          localStorage.removeItem('token');
          setToken(null);
        }
      }
    }
  }, [token]);

  const handleLogin = (newToken: string, newUser: User): void => {
    localStorage.setItem('token', newToken);
    localStorage.setItem('user', JSON.stringify(newUser));
    setToken(newToken);
    setUser(newUser);
    setShowLogin(false);
  };

  const handleLogout = (): void => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setToken(null);
    setUser(null);
    setShowLogin(false);
  };

  const handleShowLogin = (): void => {
    setShowLogin(true);
  };

  const handleBackToLanding = (): void => {
    setShowLogin(false);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {token ? (
        <Dashboard user={user} onLogout={handleLogout} />
      ) : showLogin ? (
        <Login onLogin={handleLogin} onBack={handleBackToLanding} />
      ) : (
        <LandingPage onShowLogin={handleShowLogin} />
      )}
    </ThemeProvider>
  );
}

export default App;
