import React, { useState } from 'react';
import { 
  <PERSON>, 
  Con<PERSON>er, 
  Card, 
  CardContent, 
  Typography, 
  TextField, 
  Button, 
  Alert,
  Link
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { LoginProps, AuthResponse, ApiError, LoginCredentials, RegisterCredentials } from '../types';

const Login: React.FC<LoginProps> = ({ onLogin, onBack }) => {
  const { t } = useTranslation();
  const [isLogin, setIsLogin] = useState<boolean>(true);
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [name, setName] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';
      const body: LoginCredentials | RegisterCredentials = isLogin 
        ? { email, password }
        : { email, password, name };
        
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        const data: AuthResponse = await response.json();
        onLogin(data.token, data.user);
      } else {
        const errorData: ApiError = await response.json();
        setError(errorData.error || t('auth.errorOccurred'));
      }
    } catch (err) {
      console.error('Network error:', err);
      setError(t('auth.networkError'));
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = (): void => {
    setIsLogin(!isLogin);
    setError('');
  };

  return (
    <Box sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', bgcolor: 'background.default' }}>
      <Container maxWidth="sm">
        <Card>
          <CardContent sx={{ p: 4 }}>
            <Box textAlign="center" mb={4}>
              <Box component="img" src="/logo.png" alt="Pigeon Squad" sx={{ width: 80, height: 80, mb: 1 }} />
              <Typography variant="h4" component="h1" gutterBottom>{t('app.title')}</Typography>
              <Typography variant="h5" component="h2" gutterBottom>
                {isLogin ? t('auth.welcomeBack') : t('auth.joinApp')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {isLogin 
                  ? t('auth.signInDescription')
                  : t('auth.signUpDescription')
                }
              </Typography>
            </Box>
            
            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
              {!isLogin && (
                <TextField
                  fullWidth
                  label={t('auth.fullName')}
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  sx={{ mb: 2 }}
                />
              )}
              
              <TextField
                fullWidth
                label={t('auth.emailAddress')}
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label={t('auth.password')}
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                sx={{ mb: 2 }}
              />
              
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              <Button 
                type="submit" 
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                sx={{ mb: 2 }}
              >
                {loading ? t('auth.pleaseWait') : (isLogin ? t('auth.signIn') : t('auth.createAccount'))}
              </Button>
            </Box>
            
            <Box textAlign="center">
              <Typography variant="body2">
                {isLogin ? t('auth.noAccount') : t('auth.haveAccount')}
                <Link component="button" type="button" onClick={toggleMode} sx={{ cursor: 'pointer' }}>
                  {isLogin ? t('auth.signUp') : t('auth.signIn')}
                </Link>
              </Typography>
              
              <Button 
                onClick={onBack}
                variant="outlined"
                size="small"
                sx={{ mt: 2 }}
              >
                {t('auth.backToHome')}
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
};

export default Login;