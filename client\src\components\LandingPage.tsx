import React from 'react';
import { Box, Container, Typo<PERSON>, <PERSON><PERSON>, Card, CardContent, Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { LandingPageProps } from '../types';

const LandingPage: React.FC<LandingPageProps> = ({ onShowLogin }) => {
  const { t } = useTranslation();
  
  const scrollToFeatures = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    const featuresSection = document.getElementById('features-section');
    if (featuresSection) {
      featuresSection.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <Box>
      <Box sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', bgcolor: 'background.default' }}>
        <Container maxWidth="md">
          <Box textAlign="center" py={8}>
            <img src="/logo.png" alt="Pigeon Squad" style={{ width: 100, height: 100, marginBottom: 8, display: 'block', margin: '0 auto 8px auto' }} />
            <Typography variant="h4" component="h1" gutterBottom>{t('app.title')}</Typography>
            <Typography variant="h6" color="text.secondary" gutterBottom>{t('landing.subtitle')}</Typography>
            
            <Typography variant="h4" component="h2" sx={{ mt: 4, mb: 2, fontWeight: 600 }}>
              {t('landing.headline')}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}>
              {t('landing.description')}
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button variant="contained" size="large" onClick={onShowLogin}>
                {t('landing.getStarted')}
              </Button>
              <Button variant="outlined" size="large" onClick={scrollToFeatures}>
                {t('landing.learnMore')}
              </Button>
            </Box>
          </Box>
        </Container>
      </Box>
      
      <Box id="features-section" sx={{ py: 8, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Typography variant="h4" component="h2" textAlign="center" gutterBottom sx={{ mb: 6 }}>
            {t('landing.howSproutHelps')}
          </Typography>
          <Grid container spacing={4}>
            {[
              { icon: '📧', titleKey: 'landing.features.smartEmail.title', descKey: 'landing.features.smartEmail.desc' },
              { icon: '📅', titleKey: 'landing.features.autoReminders.title', descKey: 'landing.features.autoReminders.desc' },
              { icon: '👨👩👧👦', titleKey: 'landing.features.familySharing.title', descKey: 'landing.features.familySharing.desc' },
              { icon: '🎯', titleKey: 'landing.features.prioritySorting.title', descKey: 'landing.features.prioritySorting.desc' },
              { icon: '📱', titleKey: 'landing.features.multiPlatform.title', descKey: 'landing.features.multiPlatform.desc' },
              { icon: '🧠', titleKey: 'landing.features.learningAI.title', descKey: 'landing.features.learningAI.desc' }
            ].map((feature, index) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                <Card sx={{ height: '100%', textAlign: 'center' }}>
                  <CardContent sx={{ p: 3 }}>
                    <Typography variant="h2" sx={{ fontSize: '3rem', mb: 2 }}>{feature.icon}</Typography>
                    <Typography variant="h6" component="h3" gutterBottom>{t(feature.titleKey)}</Typography>
                    <Typography variant="body2" color="text.secondary">{t(feature.descKey)}</Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
      
      <Box sx={{ py: 8, bgcolor: 'primary.main', color: 'white' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4} textAlign="center">
            {[
              { number: '98%', labelKey: 'landing.stats.accuracy' },
              { number: '2.5k+', labelKey: 'landing.stats.families' },
              { number: '15min', labelKey: 'landing.stats.timeSaved' },
              { number: 'Zero', labelKey: 'landing.stats.missedEvents' }
            ].map((stat, index) => (
              <Grid size={{ xs: 6, md: 3 }} key={index}>
                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {stat.number}
                </Typography>
                <Typography variant="body1">{t(stat.labelKey)}</Typography>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>
    </Box>
  );
};

export default LandingPage;