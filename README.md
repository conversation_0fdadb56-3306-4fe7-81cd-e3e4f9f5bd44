# Pigeon Squad (working title) - Family Activity Monitor

A minimal Node.js Express service with React frontend for monitoring family activities through email processing.

## Features

- Basic authentication (register/login)
- SQLite database with Prisma ORM
- Gmail integration for email processing
- Real-time email monitoring via IMAP
- React frontend dashboard
- JWT-based authentication

## Setup

1. Install dependencies for all packages:
```bash
npm run install:all
# This installs dependencies for root, server, and client
```

2. Configure environment variables:
```bash
# Copy the root environment template
cp .env.example .env
# Copy the server environment template
cp server/.env.example server/.env
# Edit server/.env with your Gmail API credentials and app name
```

3. Database Setup (Prisma + SQLite):
```bash
npm run db:migrate    # Create database and run migrations
npm run db:generate   # Generate Prisma client
```

4. Gmail IMAP Setup:
   - Enable 2-factor authentication on your Gmail account
   - Generate an App Password:
     - Go to [Google Account Settings](https://myaccount.google.com/)
     - Security > 2-Step Verification > App passwords
     - Generate password for "Mail"
     - Copy the 16-character password
   - Add to your `server/.env` file:
     ```
     GMAIL_EMAIL=<EMAIL>
     GMAIL_APP_PASSWORD=your-16-char-app-password
     ```

5. Run the application:
```bash
npm run dev
```

## Project Structure

```
sprout/
├── package.json          # Root workspace with npm script shortcuts
├── server/               # Backend server (Node.js + Express)
│   ├── package.json      # Server dependencies
│   ├── nodemon.json      # Development configuration
│   ├── .env.example      # Server environment template
│   ├── index.ts          # Express server entry point
│   ├── db.ts             # Prisma database client
│   ├── routes/
│   │   ├── auth.ts       # Authentication routes
│   │   ├── email.ts      # Email processing routes
│   │   └── monitor.ts    # Email monitoring routes
│   ├── middleware/
│   │   └── auth.ts       # JWT middleware
│   └── services/
│       └── imapMonitor.ts # IMAP email monitoring service
├── client/               # Frontend React app (Vite)
│   ├── package.json      # Client dependencies
│   ├── vite.config.ts    # Vite configuration
│   ├── src/
│   │   ├── App.tsx       # Main React app
│   │   └── components/   # React components
│   └── dist/             # Built client files
├── prisma/               # Shared database layer
│   ├── schema.prisma     # Database schema
│   └── migrations/       # Database migrations
└── specs/                # Project specifications
```

## API Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/email/messages` - Fetch Gmail messages
- `GET /api/email/messages/:id` - Get specific message

## Available Scripts

### Development
```bash
npm run dev           # Start both server and client in development mode
npm run server        # Start only the server in development mode
npm run client        # Start only the client in development mode
```

### Building
```bash
npm run build         # Build both server and client for production
npm run build:server  # Build only the server
npm run build:client  # Build only the client
```

### Production
```bash
npm start             # Start the production server
npm run start:server  # Start the production server
npm run start:client  # Start the client preview server
```

### Testing
```bash
npm test              # Run tests for both server and client
npm run test:server   # Run server tests
npm run test:client   # Run client tests
```

### Database Commands
```bash
npm run db:migrate    # Run database migrations
npm run db:generate   # Generate Prisma client
npm run db:studio     # Open database browser
npm run db:reset      # Reset database (careful!)
```

### Utility Commands
```bash
npm run install:all   # Install dependencies for all packages
npm run clean         # Remove all node_modules and build folders
npm run clean:install # Clean and reinstall all dependencies
```

## Configuration

### App Name
The app name is configurable via environment variables:
- `APP_NAME` - Used by the server (set in `server/.env`)
- `VITE_APP_NAME` - Used by the React frontend (set in `client/.env`)

To change the app name, update these values in the respective `.env` files.

### Environment Files
- **Root `.env`**: Shared environment variables (if any)
- **Server `server/.env`**: Server-specific configuration (database, JWT, Gmail)
- **Client `client/.env`**: Client-specific configuration (API endpoints, app name)

## Next Steps

- Implement AI processing pipeline
- Add notification system
- Enhance UI/UX
- Add family management features
- Migrate to PostgreSQL for production

