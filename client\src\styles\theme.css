/* Pigeon Squad Family Activity Monitor - Theme Variables */
:root {
  /* Primary Colors - Pigeon/Sky Theme */
  --primary-blue: #3B82F6;
  --primary-blue-dark: #1E40AF;
  --primary-blue-light: #93C5FD;
  --accent-purple: #8B5CF6;
  --accent-purple-light: #C4B5FD;
  
  /* Neutral Colors */
  --white: #FFFFFF;
  --light-gray: #F5F5F5;
  --medium-gray: #E0E0E0;
  --dark-gray: #757575;
  --text-dark: #212121;
  --text-light: #666666;
  
  /* Status Colors */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;
  
  /* Shadows */
  --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
  --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
  
  /* Border Radius */
  --radius-small: 4px;
  --radius-medium: 8px;
  --radius-large: 12px;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Typography */
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  --font-size-xxl: 32px;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  color: var(--text-dark);
  background-color: var(--light-gray);
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

/* Utility Classes */
.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-medium);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-primary {
  background-color: var(--primary-blue);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
  background-color: var(--primary-blue);
  color: var(--white);
}

.btn-accent {
  background-color: var(--accent-purple);
  color: var(--white);
}

.btn-accent:hover {
  background-color: #7C3AED;
}

.card {
  background: var(--white);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.text-center {
  text-align: center;
}

.text-muted {
  color: var(--text-light);
}

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.flex {
  display: flex;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

/* Form Styles */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--medium-gray);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-base);
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-dark);
}

/* Priority Indicators */
.priority-high {
  border-left: 4px solid var(--error);
}

.priority-medium {
  border-left: 4px solid var(--warning);
}

.priority-low {
  border-left: 4px solid var(--info);
}

/* Status Badges */
.badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.badge-success {
  background-color: var(--success);
  color: var(--white);
}

.badge-warning {
  background-color: var(--warning);
  color: var(--white);
}

.badge-info {
  background-color: var(--info);
  color: var(--white);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}