import Imap from 'imap';
import { simple<PERSON><PERSON>er, ParsedMail } from 'mailparser';

class ImapMonitorService {
  private imap: any;
  private isConnected: boolean = false;

  constructor() {
    this.imap = new Imap({
      user: process.env.GMAIL_EMAIL!,
      password: process.env.GMAIL_APP_PASSWORD!,
      host: 'imap.gmail.com',
      port: 993,
      tls: true,
      tlsOptions: { rejectUnauthorized: false }
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.imap.once('ready', () => {
      console.log('IMAP connection ready');
      this.isConnected = true;
      this.openInbox();
    });

    this.imap.once('error', (err: Error) => {
      console.error('IMAP connection error:', err.message);
    });

    this.imap.once('end', () => {
      console.log('IMAP connection ended');
      this.isConnected = false;
    });
  }

  start(): void {
    console.log('Starting IMAP email monitor...');
    this.imap.connect();
  }

  stop(): void {
    console.log('Stopping IMAP monitor...');
    if (this.isConnected) {
      this.imap.end();
    }
    // Force close after timeout
    setTimeout(() => {
      if (this.isConnected) {
        console.log('Force closing IMAP connection...');
        this.imap.destroy();
      }
    }, 2000);
  }

  private openInbox(): void {
    this.imap.openBox('INBOX', false, (err: any, box: any) => {
      if (err) {
        console.error('Error opening inbox:', err);
        return;
      }

      console.log(`Monitoring inbox with ${box.messages.total} messages`);
      this.processExistingUnreadMessages();
      this.watchForNewMessages();
    });
  }

  private watchForNewMessages(): void {
    this.imap.on('mail', (numNewMsgs: number) => {
      console.log(`${numNewMsgs} new message(s) received`);
      this.fetchNewMessages();
    });
  }

  private processExistingUnreadMessages(): void {
    this.imap.search(['UNSEEN'], (err: any, results: any) => {
      if (err) {
        console.error('Error searching for unread messages:', err);
        return;
      }

      if (results.length > 0) {
        console.log(`Found ${results.length} existing unread messages`);
        this.fetchMessages(results);
      }
    });
  }

  private fetchNewMessages(): void {
    const fetch = this.imap.seq.fetch('*:*', {
      bodies: '',
      markSeen: false
    });

    fetch.on('message', (msg: any, seqno: any) => {
      this.processMessage(msg, seqno);
    });

    fetch.once('error', (err: any) => {
      console.error('Fetch error:', err);
    });
  }

  private fetchMessages(uids: number[]): void {
    const fetch = this.imap.fetch(uids, {
      bodies: '',
      markSeen: true
    });

    fetch.on('message', (msg: any, seqno: any) => {
      this.processMessage(msg, seqno);
    });

    fetch.once('error', (err: any) => {
      console.error('Fetch error:', err);
    });
  }

  private processMessage(msg: any, seqno: any): void {
    msg.on('body', (stream: any) => {
      simpleParser(stream, (err: any, parsed: ParsedMail) => {
        if (err) {
          console.error('Error parsing message:', err);
          return;
        }

        console.log('=== EMAIL PROCESSED ===');
        console.log(`From: ${parsed.from?.text || 'Unknown'}`);
        console.log(`To: ${Array.isArray(parsed.to) ? parsed.to.map(t => t.text).join(', ') : parsed.to?.text || 'Unknown'}`);
        console.log(`Subject: ${parsed.subject || 'No subject'}`);
        console.log(`Body: ${(parsed.text || '').substring(0, 200)}...`);
        console.log(`Date: ${parsed.date}`);
        console.log('======================');
      });
    });

    msg.once('attributes', (attrs: any) => {
      this.imap.addFlags(attrs.uid, '\\Seen', (err: any) => {
        if (err) console.error('Error marking as read:', err);
      });
    });
  }
}

export default ImapMonitorService;